/**
 * XState Layout Machine cho Agent Configuration
 * Quản lý layout state dựa trên TypeAgent config và responsive breakpoints
 */

import { createMachine, assign } from 'xstate';
import { TypeAgentConfig } from '../types';

/**
 * Thông tin component với priority
 */
export interface ComponentInfo {
  name: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  condition?: keyof TypeAgentConfig;
  always?: boolean;
}

/**
 * Layout context
 */
export interface LayoutContext {
  typeAgentConfig: TypeAgentConfig;
  availableComponents: {
    left: ComponentInfo[];
    right: ComponentInfo[];
  };
  layoutMode: 'BALANCED' | 'RIGHT_HEAVY' | 'LEFT_ONLY' | 'RIGHT_ONLY' | 'EMPTY';
  columnRatio: [number, number]; // [left%, right%]
  breakpoint: 'mobile' | 'tablet' | 'desktop';
}

/**
 * Layout machine events
 */
export type LayoutEvent =
  | { type: 'CONFIG_CHANGED'; config: TypeAgentConfig }
  | { type: 'RESIZE'; breakpoint: 'mobile' | 'tablet' | 'desktop' }
  | { type: 'FORCE_LAYOUT'; mode: LayoutContext['layoutMode'] };

/**
 * Component definitions với priority - ModelConfig được loại bỏ vì luôn ở header
 */
const COMPONENT_DEFINITIONS: ComponentInfo[] = [
  // Medium Priority - Ưu tiên cột trái
  { name: 'IntegrationConfig', priority: 'MEDIUM', condition: 'hasResources' },
  { name: 'StrategyConfig', priority: 'MEDIUM', condition: 'hasStrategy' },

  // Low Priority - Ưu tiên cột phải
  { name: 'ProfileConfig', priority: 'LOW', condition: 'hasProfile' },
  { name: 'ResponseConfig', priority: 'LOW', condition: 'hasOutput' },
  { name: 'ConvertConfig', priority: 'LOW', condition: 'hasConversion' },
  { name: 'MultiAgentConfig', priority: 'LOW', condition: 'hasMultiAgent' },
];

/**
 * Phân tích components có sẵn dựa trên TypeAgent config
 */
const analyzeAvailableComponents = (config: TypeAgentConfig): { left: ComponentInfo[]; right: ComponentInfo[] } => {
  const visibleComponents = COMPONENT_DEFINITIONS.filter(comp => {
    if (comp.always) return true;
    if (!comp.condition) return true;
    return config[comp.condition] === true;
  });

  // Phân bổ components theo priority
  const left: ComponentInfo[] = [];
  const right: ComponentInfo[] = [];

  // High priority luôn vào cột trái
  const highPriority = visibleComponents.filter(c => c.priority === 'HIGH');
  left.push(...highPriority);

  // Medium priority vào cột trái
  const mediumPriority = visibleComponents.filter(c => c.priority === 'MEDIUM');
  left.push(...mediumPriority);

  // Low priority vào cột phải
  const lowPriority = visibleComponents.filter(c => c.priority === 'LOW');
  right.push(...lowPriority);

  return { left, right };
};

/**
 * Layout State Machine
 */
export const layoutMachine = createMachine<LayoutContext, LayoutEvent>({
  id: 'agentConfigLayout',
  initial: 'analyzing',
  context: {
    typeAgentConfig: {},
    availableComponents: { left: [], right: [] },
    layoutMode: 'BALANCED',
    columnRatio: [60, 40],
    breakpoint: 'desktop'
  },
  states: {
    analyzing: {
      entry: assign({
        availableComponents: ({ context }) => {
          return analyzeAvailableComponents(context.typeAgentConfig);
        }
      }),
      always: [
        { target: 'empty', guard: 'hasNoComponents' },
        { target: 'leftOnly', guard: 'hasOnlyLeftComponents' },
        { target: 'rightOnly', guard: 'hasOnlyRightComponents' },
        { target: 'rightHeavy', guard: 'shouldBeRightHeavy' },
        { target: 'balanced' }
      ]
    },

    empty: {
      entry: assign({
        layoutMode: 'EMPTY',
        columnRatio: [100, 0]
      }),
      on: {
        CONFIG_CHANGED: {
          target: 'analyzing',
          actions: assign({
            typeAgentConfig: ({ event }) =>
              event.type === 'CONFIG_CHANGED' ? event.config : {}
          })
        },
        RESIZE: {
          target: 'analyzing',
          actions: assign({
            breakpoint: ({ event }) =>
              event.type === 'RESIZE' ? event.breakpoint : 'desktop'
          })
        }
      }
    },

    leftOnly: {
      entry: assign({
        layoutMode: 'LEFT_ONLY',
        columnRatio: [100, 0]
      }),
      on: {
        CONFIG_CHANGED: {
          target: 'analyzing',
          actions: assign({
            typeAgentConfig: ({ event }) =>
              event.type === 'CONFIG_CHANGED' ? event.config : {}
          })
        },
        RESIZE: {
          target: 'analyzing',
          actions: assign({
            breakpoint: ({ event }) =>
              event.type === 'RESIZE' ? event.breakpoint : 'desktop'
          })
        }
      }
    },

    rightOnly: {
      entry: assign({
        layoutMode: 'RIGHT_ONLY',
        columnRatio: [0, 100]
      }),
      on: {
        CONFIG_CHANGED: {
          target: 'analyzing',
          actions: assign({
            typeAgentConfig: ({ event }) =>
              event.type === 'CONFIG_CHANGED' ? event.config : {}
          })
        },
        RESIZE: {
          target: 'analyzing',
          actions: assign({
            breakpoint: ({ event }) =>
              event.type === 'RESIZE' ? event.breakpoint : 'desktop'
          })
        }
      }
    },

    rightHeavy: {
      entry: assign({
        layoutMode: 'RIGHT_HEAVY',
        columnRatio: [40, 60]
      }),
      on: {
        CONFIG_CHANGED: {
          target: 'analyzing',
          actions: assign({
            typeAgentConfig: ({ event }) =>
              event.type === 'CONFIG_CHANGED' ? event.config : {}
          })
        },
        RESIZE: {
          target: 'analyzing',
          actions: assign({
            breakpoint: ({ event }) =>
              event.type === 'RESIZE' ? event.breakpoint : 'desktop'
          })
        }
      }
    },

    balanced: {
      entry: assign({
        layoutMode: 'BALANCED',
        columnRatio: [60, 40]
      }),
      on: {
        CONFIG_CHANGED: {
          target: 'analyzing',
          actions: assign({
            typeAgentConfig: ({ event }) =>
              event.type === 'CONFIG_CHANGED' ? event.config : {}
          })
        },
        RESIZE: {
          target: 'analyzing',
          actions: assign({
            breakpoint: ({ event }) =>
              event.type === 'RESIZE' ? event.breakpoint : 'desktop'
          })
        }
      }
    }
  }
}, {
  guards: {
    hasNoComponents: ({ context }) => {
      return context.availableComponents.left.length === 0 &&
             context.availableComponents.right.length === 0;
    },

    hasOnlyLeftComponents: ({ context }) => {
      return context.availableComponents.left.length > 0 &&
             context.availableComponents.right.length === 0;
    },

    hasOnlyRightComponents: ({ context }) => {
      return context.availableComponents.left.length === 0 &&
             context.availableComponents.right.length > 0;
    },

    shouldBeRightHeavy: ({ context }) => {
      // Nếu cột trái chỉ có 1 component và cột phải có >= 3 components
      return context.availableComponents.left.length === 1 &&
             context.availableComponents.right.length >= 3;
    }
  }
});
