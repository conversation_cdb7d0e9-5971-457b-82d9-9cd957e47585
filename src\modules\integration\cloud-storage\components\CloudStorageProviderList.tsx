import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Table,
  Input,
  Select,
  EmptyState,
  ConfirmDeleteModal,
  ResponsiveGrid,
} from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { CLOUD_STORAGE_PROVIDER_TYPES } from '../constants';
import { useCloudStorageProviders, useDeleteCloudStorageProvider, useTestCloudStorageProvider } from '../hooks';
import type { CloudStorageProviderConfiguration, CloudStorageProviderQueryParams } from '../types';
import { formatDate } from '@/shared/utils/date';

interface CloudStorageProviderListProps {
  onCreateNew?: () => void;
  onEdit?: (provider: CloudStorageProviderConfiguration) => void;
}

/**
 * <PERSON>h sách Cloud Storage Providers
 */
const CloudStorageProviderList: React.FC<CloudStorageProviderListProps> = ({
  onCreateNew,
  onEdit
}) => {
  const { t } = useTranslation(['integration', 'common']);

  const [queryParams, setQueryParams] = useState<CloudStorageProviderQueryParams>({
    page: 1,
    limit: 10,
    search: '',
    providerType: undefined,
    isActive: undefined,
  });

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [providerToDelete, setProviderToDelete] = useState<CloudStorageProviderConfiguration | null>(null);

  // Hooks
  const { data: providersData, isLoading } = useCloudStorageProviders(queryParams);
  const deleteMutation = useDeleteCloudStorageProvider();
  const testMutation = useTestCloudStorageProvider();

  // Handler functions
  const handleTest = useCallback(async (provider: CloudStorageProviderConfiguration) => {
    try {
      const result = await testMutation.mutateAsync({ id: provider.id });
      if (result.success) {
        // TODO: Show success toast
        console.log('Test successful:', result);
      } else {
        // TODO: Show error toast
        console.error('Test failed:', result.message);
      }
    } catch (error) {
      console.error('Test error:', error);
      // TODO: Show error toast
    }
  }, [testMutation]);

  const handleEdit = useCallback((provider: CloudStorageProviderConfiguration) => {
    onEdit?.(provider);
  }, [onEdit]);

  const handleDeleteClick = (provider: CloudStorageProviderConfiguration) => {
    setProviderToDelete(provider);
    setDeleteModalOpen(true);
  };

  // Table columns
  const columns = useMemo(() => [
    {
      title: t('integration:cloudStorage.details.provider'),
      dataIndex: 'providerType',
      key: 'providerType',
      render: (providerType: string) => {
        const provider = CLOUD_STORAGE_PROVIDER_TYPES[providerType as keyof typeof CLOUD_STORAGE_PROVIDER_TYPES];
        return (
          <div className="flex items-center gap-2">
            <Icon name="cloud" size="sm" className="text-primary" />
            <span>{provider?.displayName || providerType}</span>
          </div>
        );
      },
    },
    {
      title: t('integration:cloudStorage.form.providerName.label'),
      dataIndex: 'providerName',
      key: 'providerName',
    },
    {
      title: t('integration:cloudStorage.status.active'),
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <span className={`px-2 py-1 rounded text-xs ${
          isActive
            ? 'bg-green-100 text-green-800'
            : 'bg-gray-100 text-gray-800'
        }`}>
          {isActive
            ? t('integration:cloudStorage.status.active')
            : t('integration:cloudStorage.status.inactive')
          }
        </span>
      ),
    },
    {
      title: t('integration:cloudStorage.details.lastSync'),
      dataIndex: 'lastSyncAt',
      key: 'lastSyncAt',
      render: (lastSyncAt: string) =>
        lastSyncAt ? formatDate(lastSyncAt) : t('integration:cloudStorage.details.neverSynced'),
    },
    {
      title: t('integration:cloudStorage.details.created'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (createdAt: string) => formatDate(createdAt),
    },
    {
      title: t('common:actions'),
      key: 'actions',
      render: (_: unknown, record: CloudStorageProviderConfiguration) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleTest(record)}
            disabled={testMutation.isPending}
          >
            <Icon name="zap" size="sm" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(record)}
          >
            <Icon name="edit" size="sm" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(record)}
            className="text-red-600 hover:text-red-700"
          >
            <Icon name="trash" size="sm" />
          </Button>
        </div>
      ),
    },
  ], [t, testMutation.isPending, handleEdit, handleTest]);

  // Data table configuration
  const dataTable = useDataTable(useDataTableConfig({ columns }));

  // Provider type options for filter
  const providerTypeOptions = [
    { value: '', label: t('integration:cloudStorage.filters.all') },
    ...Object.values(CLOUD_STORAGE_PROVIDER_TYPES).map(provider => ({
      value: provider.id,
      label: provider.displayName,
    })),
  ];

  // Status options for filter
  const statusOptions = [
    { value: '', label: t('integration:cloudStorage.filters.all') },
    { value: 'true', label: t('integration:cloudStorage.filters.active') },
    { value: 'false', label: t('integration:cloudStorage.filters.inactive') },
  ];

  const handleSearch = (searchTerm: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: searchTerm,
      page: 1,
    }));
  };

  const handleProviderTypeFilter = (providerType: string) => {
    setQueryParams(prev => ({
      ...prev,
      providerType: providerType || undefined,
      page: 1,
    }));
  };

  const handleStatusFilter = (status: string) => {
    setQueryParams(prev => ({
      ...prev,
      isActive: status ? status === 'true' : undefined,
      page: 1,
    }));
  };



  const handleDeleteConfirm = async () => {
    if (!providerToDelete) return;

    try {
      await deleteMutation.mutateAsync(providerToDelete.id);
      setDeleteModalOpen(false);
      setProviderToDelete(null);
      // TODO: Show success toast
    } catch (error) {
      console.error('Delete error:', error);
      // TODO: Show error toast
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModalOpen(false);
    setProviderToDelete(null);
  };

  const providers = providersData?.data?.items || [];
  const hasProviders = providers.length > 0;

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <Card className="mb-6">
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <Typography variant="h3" className="mb-2">
                {t('integration:cloudStorage.list.title')}
              </Typography>
              <Typography variant="body1" className="text-muted-foreground">
                {t('integration:cloudStorage.list.description')}
              </Typography>
            </div>

            <Button
              variant="primary"
              onClick={onCreateNew}
            >
              <Icon name="plus" size="sm" className="mr-2" />
              {t('integration:cloudStorage.list.createNew')}
            </Button>
          </div>
        </div>
      </Card>

      {/* Filters */}
      {hasProviders && (
        <Card className="mb-6">
          <div className="p-6">
            <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }} gap={4}>
              {/* Search */}
              <Input
                type="text"
                placeholder={t('integration:cloudStorage.list.searchPlaceholder')}
                value={queryParams.search || ''}
                onChange={(e) => handleSearch(e.target.value)}
                fullWidth
              />

              {/* Provider Type Filter */}
              <Select
                value={queryParams.providerType || ''}
                onChange={(value) => handleProviderTypeFilter(value as string)}
                options={providerTypeOptions}
                placeholder={t('integration:cloudStorage.filters.byProvider')}
                fullWidth
              />

              {/* Status Filter */}
              <Select
                value={queryParams.isActive !== undefined ? queryParams.isActive.toString() : ''}
                onChange={(value) => handleStatusFilter(value as string)}
                options={statusOptions}
                placeholder={t('integration:cloudStorage.filters.all')}
                fullWidth
              />
            </ResponsiveGrid>

            {/* Results Count */}
            {providersData?.data && (
              <div className="mt-4 pt-4 border-t">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('integration:cloudStorage.list.resultsCount', {
                    count: providers.length,
                    total: providersData.data.total || 0,
                  })}
                </Typography>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Content */}
      <Card>
        {hasProviders ? (
          <div className="p-6">
            <Table
              columns={dataTable.columnVisibility.visibleTableColumns}
              data={providers}
              loading={isLoading}
              pagination={dataTable.pagination}
            />
          </div>
        ) : (
          <div className="p-8">
            <EmptyState
              icon="cloud"
              title={t('integration:cloudStorage.empty.noConfigurations')}
              description={t('integration:cloudStorage.empty.noConfigurationsDescription')}
              actions={
                <Button
                  variant="primary"
                  onClick={onCreateNew}
                >
                  <Icon name="plus" size="sm" className="mr-2" />
                  {t('integration:cloudStorage.empty.createFirst')}
                </Button>
              }
            />
          </div>
        )}
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        open={deleteModalOpen}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        title={t('integration:cloudStorage.modal.deleteTitle')}
        description={t('integration:cloudStorage.modal.deleteDescription', {
          name: providerToDelete?.providerName || '',
        })}
        confirmText={t('integration:cloudStorage.modal.deleteConfirm')}
        isLoading={deleteMutation.isPending}
      />
    </div>
  );
};

export default CloudStorageProviderList;
